<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;7650ab46-ea1d-4bfa-bb21-f459d8fe9f03&quot;,&quot;conversations&quot;:{&quot;817700cd-87f7-416d-9753-4cd229021d51&quot;:{&quot;id&quot;:&quot;817700cd-87f7-416d-9753-4cd229021d51&quot;,&quot;createdAtIso&quot;:&quot;2025-06-09T13:20:14.682Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-09T13:20:14.682Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;5a155973-9caa-42ea-8bfc-740c628332bf&quot;},&quot;7650ab46-ea1d-4bfa-bb21-f459d8fe9f03&quot;:{&quot;id&quot;:&quot;7650ab46-ea1d-4bfa-bb21-f459d8fe9f03&quot;,&quot;createdAtIso&quot;:&quot;2025-06-09T13:20:14.805Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-09T13:20:14.806Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;d581d4d8-7a46-4384-b250-f18926c98916&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[],&quot;sortConversationsBy&quot;:&quot;lastMessageTimestamp&quot;}" />
      </map>
    </option>
  </component>
</project>
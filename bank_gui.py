from tabulate import tabulate as tab
import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox

class Bank:
    def __init__(self):
        self.lista_kont = []

    def utworzKonto(self, imie=None , nazwisko=None , numer_konta=None , saldo=0):
        blokada = len([x for x in self.lista_kont if x.get_numer() == numer_konta])
        if blokada:
            print('Numer zajęty')
        else:
            K = KontoBankowe(imie, nazwisko, numer_konta, saldo)
            self.lista_kont.append(K)

    def printKonta (self):
        lista=[['Imię','Nazwisko','Numer konta','Saldo']]
        for konto in self.lista_kont:
            lista.append([konto.imie,konto.nazwisko,konto.get_numer(),konto.get_saldo()])
        print('Lista kont: ' )
        print(tab(lista, headers='firstrow', tablefmt='grid'))

    def przelew(self, nSource, nTarget, kwota):
        # source = next(filter(lambda x: x.get_numer() == nSource, self.lista_kont))
        source = [x for x in self.lista_kont if x.get_numer() == nSource][0]
        target = next(filter(lambda x: x.get_numer() == nTarget, self.lista_kont))
        source.przelew(target,kwota)

    def wplata(self, nSource, kwota):
        source = [x for x in self.lista_kont if x.get_numer() == nSource][0]
        source.wplata(kwota)

    def wyplata(self, nSource, kwota):
        source = [x for x in self.lista_kont if x.get_numer() == nSource][0]
        source.wyplata(kwota)

    def historia_konta(self, nSource):
        source = [x for x in self.lista_kont if x.get_numer() == nSource][0]
        source.print_hist()

class KontoBankowe:
    def __init__(self , imie=None , nazwisko=None , numer_konta=None , saldo=0 ):
        self.imie = imie
        self.nazwisko = nazwisko
        self.__numer_konta = numer_konta # pole prywatne
        self.__saldo = saldo # pole prywatne
        self.__historia = [['Operacja','Kwota','Saldo']] # pole prywatne

    def wplata(self , kwota ):
        self.__saldo += kwota
        self.__historia.append(['+',kwota,self.__saldo])

    def wyplata(self , kwota ):
        if self.__saldo >= kwota:
            self.__saldo -= kwota
            self.__historia.append(['-',kwota,self.__saldo])
            return 1
        else:
            print("Nie masz wystarczajacych srodkow na koncie")
            return 0

    def get_saldo (self):
        return self.__saldo

    def get_numer (self):
        return self.__numer_konta

    def przelew(self,target,kwota):
        if self.wyplata(kwota):
            target.wplata(kwota)
        else:
            print("Przelew nieudany")

    def print_hist (self):
        print('Historia konta: ' + self.imie + ' ' + self.nazwisko + ' ' + str(self.__numer_konta))
        print(tab(self.__historia, headers='firstrow', tablefmt='grid'))


class BankGUI:
    def __init__(self):
        self.bank = Bank()
        self.setup_gui()

    def setup_gui(self):
        # Configure customtkinter appearance
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")

        # Create main window
        self.root = ctk.CTk()
        self.root.title("System Bankowy")
        self.root.geometry("800x700")

        # Create main frame with scrollable content
        self.main_frame = ctk.CTkScrollableFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title_label = ctk.CTkLabel(self.main_frame, text="System Bankowy",
                                  font=ctk.CTkFont(size=24, weight="bold"))
        title_label.pack(pady=(0, 20))

        # Create Account Section
        self.create_account_section()

        # Account Operations Section
        self.create_operations_section()

        # Transfer Section
        self.create_transfer_section()

        # Display Section
        self.create_display_section()

        # Results Display
        self.create_results_section()

    def create_account_section(self):
        # Create Account Frame
        account_frame = ctk.CTkFrame(self.main_frame)
        account_frame.pack(fill="x", pady=(0, 5))

        ctk.CTkLabel(account_frame, text="Utwórz Konto",
                    font=ctk.CTkFont(size=18, weight="bold")).pack(pady=(10, 10))

        # Input fields for account creation
        input_frame = ctk.CTkFrame(account_frame)
        input_frame.pack(fill="x", padx=20, pady=(0, 10))

        ctk.CTkLabel(input_frame, text="Imię:").grid(row=0, column=0, sticky="w", padx=5, pady=5)
        self.name_entry = ctk.CTkEntry(input_frame, width=150)
        self.name_entry.grid(row=0, column=1, padx=5, pady=5)

        ctk.CTkLabel(input_frame, text="Nazwisko:").grid(row=0, column=2, sticky="w", padx=5, pady=5)
        self.surname_entry = ctk.CTkEntry(input_frame, width=150)
        self.surname_entry.grid(row=0, column=3, padx=5, pady=5)

        ctk.CTkLabel(input_frame, text="Numer konta:").grid(row=1, column=0, sticky="w", padx=5, pady=5)
        self.account_number_entry = ctk.CTkEntry(input_frame, width=150)
        self.account_number_entry.grid(row=1, column=1, padx=5, pady=5)

        ctk.CTkLabel(input_frame, text="Saldo początkowe:").grid(row=1, column=2, sticky="w", padx=5, pady=5)
        self.initial_balance_entry = ctk.CTkEntry(input_frame, width=150)
        self.initial_balance_entry.grid(row=1, column=3, padx=5, pady=5)
        self.initial_balance_entry.insert(0, "0")

        # Create Account Button
        ctk.CTkButton(account_frame, text="Utwórz Konto",
                     command=self.create_account).pack(pady=(0, 10))

    def create_operations_section(self):
        # Account Operations Frame
        operations_frame = ctk.CTkFrame(self.main_frame)
        operations_frame.pack(fill="x", pady=(0, 5))

        ctk.CTkLabel(operations_frame, text="Operacje na Koncie",
                    font=ctk.CTkFont(size=18, weight="bold")).pack(pady=(10, 10))

        # Input fields for operations
        ops_input_frame = ctk.CTkFrame(operations_frame)
        ops_input_frame.pack(fill="x", padx=20, pady=(0, 10))

        ctk.CTkLabel(ops_input_frame, text="Numer konta:").grid(row=0, column=0, sticky="w", padx=5, pady=5)
        self.ops_account_entry = ctk.CTkEntry(ops_input_frame, width=150)
        self.ops_account_entry.grid(row=0, column=1, padx=5, pady=5)

        ctk.CTkLabel(ops_input_frame, text="Kwota:").grid(row=0, column=2, sticky="w", padx=5, pady=5)
        self.amount_entry = ctk.CTkEntry(ops_input_frame, width=150)
        self.amount_entry.grid(row=0, column=3, padx=5, pady=5)

        # Operation Buttons
        button_frame = ctk.CTkFrame(operations_frame)
        button_frame.pack(pady=(0, 10))

        ctk.CTkButton(button_frame, text="Wpłata",
                     command=self.deposit_money).pack(side="left", padx=5)
        ctk.CTkButton(button_frame, text="Wypłata",
                     command=self.withdraw_money).pack(side="left", padx=5)

    def create_transfer_section(self):
        # Transfer Frame
        transfer_frame = ctk.CTkFrame(self.main_frame)
        transfer_frame.pack(fill="x", pady=(0, 5))

        ctk.CTkLabel(transfer_frame, text="Przelew",
                    font=ctk.CTkFont(size=18, weight="bold")).pack(pady=(10, 10))

        # Input fields for transfer
        transfer_input_frame = ctk.CTkFrame(transfer_frame)
        transfer_input_frame.pack(fill="x", padx=20, pady=(0, 10))

        ctk.CTkLabel(transfer_input_frame, text="Konto źródłowe:").grid(row=0, column=0, sticky="w", padx=5, pady=5)
        self.source_account_entry = ctk.CTkEntry(transfer_input_frame, width=120)
        self.source_account_entry.grid(row=0, column=1, padx=5, pady=5)

        ctk.CTkLabel(transfer_input_frame, text="Konto docelowe:").grid(row=0, column=2, sticky="w", padx=5, pady=5)
        self.target_account_entry = ctk.CTkEntry(transfer_input_frame, width=120)
        self.target_account_entry.grid(row=0, column=3, padx=5, pady=5)

        ctk.CTkLabel(transfer_input_frame, text="Kwota:").grid(row=1, column=0, sticky="w", padx=5, pady=5)
        self.transfer_amount_entry = ctk.CTkEntry(transfer_input_frame, width=120)
        self.transfer_amount_entry.grid(row=1, column=1, padx=5, pady=5)

        # Transfer Button
        ctk.CTkButton(transfer_frame, text="Wykonaj Przelew",
                     command=self.transfer_money).pack(pady=(0, 10))

    def create_display_section(self):
        # Display Frame
        display_frame = ctk.CTkFrame(self.main_frame)
        display_frame.pack(fill="x", pady=(0, 5))

        ctk.CTkLabel(display_frame, text="Wyświetlanie Danych",
                    font=ctk.CTkFont(size=18, weight="bold")).pack(pady=(10, 10))

        # Input for account history
        history_input_frame = ctk.CTkFrame(display_frame)
        history_input_frame.pack(fill="x", padx=20, pady=(0, 10))

        ctk.CTkLabel(history_input_frame, text="Numer konta (dla historii):").grid(row=0, column=0, sticky="w", padx=5, pady=5)
        self.history_account_entry = ctk.CTkEntry(history_input_frame, width=150)
        self.history_account_entry.grid(row=0, column=1, padx=5, pady=5)

        # Display Buttons
        button_frame = ctk.CTkFrame(display_frame)
        button_frame.pack(pady=(0, 10))

        ctk.CTkButton(button_frame, text="Pokaż Wszystkie Konta",
                     command=self.show_all_accounts).pack(side="left", padx=5)
        ctk.CTkButton(button_frame, text="Pokaż Historię Konta",
                     command=self.show_account_history).pack(side="left", padx=5)

    def create_results_section(self):
        # Results Frame
        results_frame = ctk.CTkFrame(self.main_frame)
        results_frame.pack(fill="both", expand=True, pady=(0, 5))

        ctk.CTkLabel(results_frame, text="Wyniki",
                    font=ctk.CTkFont(size=18, weight="bold")).pack(pady=(10, 5))

        # Results Text Widget
        self.results_text = ctk.CTkTextbox(results_frame, height=200, width=700)
        self.results_text.pack(fill="both", expand=True, padx=20, pady=(0, 20))

    def create_account(self):
        try:
            name = self.name_entry.get().strip()
            surname = self.surname_entry.get().strip()
            account_number = int(self.account_number_entry.get().strip())
            initial_balance = float(self.initial_balance_entry.get().strip())

            if not name or not surname:
                messagebox.showerror("Błąd", "Imię i nazwisko są wymagane!")
                return

            # Check if account number already exists
            existing_account = any(acc.get_numer() == account_number for acc in self.bank.lista_kont)
            if existing_account:
                messagebox.showerror("Błąd", "Numer konta już istnieje!")
                return

            self.bank.utworzKonto(name, surname, account_number, initial_balance)
            self.display_result(f"Konto utworzone pomyślnie!\nImię: {name}\nNazwisko: {surname}\nNumer: {account_number}\nSaldo: {initial_balance}")

            # Clear input fields
            self.name_entry.delete(0, 'end')
            self.surname_entry.delete(0, 'end')
            self.account_number_entry.delete(0, 'end')
            self.initial_balance_entry.delete(0, 'end')
            self.initial_balance_entry.insert(0, "0")

        except ValueError:
            messagebox.showerror("Błąd", "Numer konta i saldo muszą być liczbami!")
        except Exception as e:
            messagebox.showerror("Błąd", f"Wystąpił błąd: {str(e)}")

    def deposit_money(self):
        try:
            account_number = int(self.ops_account_entry.get().strip())
            amount = float(self.amount_entry.get().strip())

            if amount <= 0:
                messagebox.showerror("Błąd", "Kwota musi być większa od zera!")
                return

            # Check if account exists
            account = self.find_account(account_number)
            if not account:
                messagebox.showerror("Błąd", "Konto o podanym numerze nie istnieje!")
                return

            self.bank.wplata(account_number, amount)
            self.display_result(f"Wpłata wykonana pomyślnie!\nKonto: {account_number}\nKwota: {amount}\nNowe saldo: {account.get_saldo()}")

            # Clear input fields
            self.ops_account_entry.delete(0, 'end')
            self.amount_entry.delete(0, 'end')

        except ValueError:
            messagebox.showerror("Błąd", "Numer konta i kwota muszą być liczbami!")
        except Exception as e:
            messagebox.showerror("Błąd", f"Wystąpił błąd: {str(e)}")

    def withdraw_money(self):
        try:
            account_number = int(self.ops_account_entry.get().strip())
            amount = float(self.amount_entry.get().strip())

            if amount <= 0:
                messagebox.showerror("Błąd", "Kwota musi być większa od zera!")
                return

            # Check if account exists
            account = self.find_account(account_number)
            if not account:
                messagebox.showerror("Błąd", "Konto o podanym numerze nie istnieje!")
                return

            if account.get_saldo() < amount:
                messagebox.showerror("Błąd", "Niewystarczające środki na koncie!")
                return

            self.bank.wyplata(account_number, amount)
            self.display_result(f"Wypłata wykonana pomyślnie!\nKonto: {account_number}\nKwota: {amount}\nNowe saldo: {account.get_saldo()}")

            # Clear input fields
            self.ops_account_entry.delete(0, 'end')
            self.amount_entry.delete(0, 'end')

        except ValueError:
            messagebox.showerror("Błąd", "Numer konta i kwota muszą być liczbami!")
        except Exception as e:
            messagebox.showerror("Błąd", f"Wystąpił błąd: {str(e)}")

    def transfer_money(self):
        try:
            source_account = int(self.source_account_entry.get().strip())
            target_account = int(self.target_account_entry.get().strip())
            amount = float(self.transfer_amount_entry.get().strip())

            if amount <= 0:
                messagebox.showerror("Błąd", "Kwota musi być większa od zera!")
                return

            if source_account == target_account:
                messagebox.showerror("Błąd", "Konto źródłowe i docelowe nie mogą być takie same!")
                return

            # Check if both accounts exist
            source_acc = self.find_account(source_account)
            target_acc = self.find_account(target_account)

            if not source_acc:
                messagebox.showerror("Błąd", "Konto źródłowe nie istnieje!")
                return

            if not target_acc:
                messagebox.showerror("Błąd", "Konto docelowe nie istnieje!")
                return

            if source_acc.get_saldo() < amount:
                messagebox.showerror("Błąd", "Niewystarczające środki na koncie źródłowym!")
                return

            self.bank.przelew(source_account, target_account, amount)
            self.display_result(f"Przelew wykonany pomyślnie!\nZ konta: {source_account}\nNa konto: {target_account}\nKwota: {amount}\nSaldo źródłowe: {source_acc.get_saldo()}\nSaldo docelowe: {target_acc.get_saldo()}")

            # Clear input fields
            self.source_account_entry.delete(0, 'end')
            self.target_account_entry.delete(0, 'end')
            self.transfer_amount_entry.delete(0, 'end')

        except ValueError:
            messagebox.showerror("Błąd", "Numery kont i kwota muszą być liczbami!")
        except Exception as e:
            messagebox.showerror("Błąd", f"Wystąpił błąd: {str(e)}")

    def show_all_accounts(self):
        try:
            if not self.bank.lista_kont:
                self.display_result("Brak kont w systemie.")
                return

            accounts_display = self.get_accounts_display()
            self.display_result(accounts_display)

        except Exception as e:
            messagebox.showerror("Błąd", f"Wystąpił błąd: {str(e)}")

    def show_account_history(self):
        try:
            account_number = int(self.history_account_entry.get().strip())

            # Check if account exists
            account = self.find_account(account_number)
            if not account:
                messagebox.showerror("Błąd", "Konto o podanym numerze nie istnieje!")
                return

            history_display = self.get_history_display(account)
            self.display_result(history_display)

            # Clear input field
            self.history_account_entry.delete(0, 'end')

        except ValueError:
            messagebox.showerror("Błąd", "Numer konta musi być liczbą!")
        except Exception as e:
            messagebox.showerror("Błąd", f"Wystąpił błąd: {str(e)}")

    def find_account(self, account_number):
        """Helper method to find account by number"""
        for account in self.bank.lista_kont:
            if account.get_numer() == account_number:
                return account
        return None

    def get_accounts_display(self):
        """Helper method to get formatted accounts display (parallel to Bank.printKonta)"""
        lista = [['Imię', 'Nazwisko', 'Numer konta', 'Saldo']]
        for konto in self.bank.lista_kont:
            lista.append([konto.imie, konto.nazwisko, konto.get_numer(), konto.get_saldo()])

        result = 'Lista kont:\n'
        result += tab(lista, headers='firstrow', tablefmt='grid')
        return result

    def get_history_display(self, account):
        """Helper method to get formatted account history (parallel to KontoBankowe.print_hist)"""
        result = f'Historia konta: {account.imie} {account.nazwisko} {account.get_numer()}\n'
        result += tab(account._KontoBankowe__historia, headers='firstrow', tablefmt='grid')
        return result

    def display_result(self, text):
        """Helper method to display results in the text widget"""
        self.results_text.delete("1.0", "end")
        self.results_text.insert("1.0", text)

    def run(self):
        """Start the GUI application"""
        self.root.mainloop()


# B = Bank()
# B.utworzKonto('Jacek','Wodecki',1234)
# B.utworzKonto('Jan','Kowalski',2345)
# B.utworzKonto('aaa','sss',2346)
# B.wplata(1234,100)
# B.printKonta()
# B.przelew(1234,2345,20)
# B.wyplata(1234,30)
# B.printKonta()
# B.historia_konta(1234)


# K1= KontoBankowe('Jacek','Wodecki',1234)
# K2= KontoBankowe('Jan','Kowalski',2345)
# print(K1.get_saldo())
# K1.wplata(100)
# print(K1.get_saldo())
# K1.wyplata(50)
# print(K1.get_saldo())
#
# K1.przelew(K2,20)
# K1.print_hist()
# K2.print_hist()
# print(K1.__numer_konta)

# Run the GUI application
if __name__ == "__main__":
    app = BankGUI()
    app.run()
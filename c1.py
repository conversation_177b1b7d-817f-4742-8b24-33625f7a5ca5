from tabulate import tabulate as tab

class Bank:
    def __init__(self):
        self.lista_kont = []

    def utworzKonto(self, imie=None , nazwisko=None , numer_konta=None , saldo=0):
        blokada = len([x for x in self.lista_kont if x.get_numer() == numer_konta])
        if blokada:
            print('Numer zajęty')
        else:
            K = KontoBankowe(imie, nazwisko, numer_konta, saldo)
            self.lista_kont.append(K)

    def printKonta (self):
        lista=[['Imię','Nazwisko','Numer konta','Saldo']]
        for konto in self.lista_kont:
            lista.append([konto.imie,konto.nazwisko,konto.get_numer(),konto.get_saldo()])
        print('Lista kont: ' )
        print(tab(lista, headers='firstrow', tablefmt='grid'))

    def przelew(self, nSource, nTarget, kwota):
        # source = next(filter(lambda x: x.get_numer() == nSource, self.lista_kont))
        source = [x for x in self.lista_kont if x.get_numer() == nSource][0]
        target = next(filter(lambda x: x.get_numer() == nTarget, self.lista_kont))
        source.przelew(target,kwota)

    def wplata(self, nSource, kwota):
        source = [x for x in self.lista_kont if x.get_numer() == nSource][0]
        source.wplata(kwota)

    def wyplata(self, nSource, kwota):
        source = [x for x in self.lista_kont if x.get_numer() == nSource][0]
        source.wyplata(kwota)

    def historia_konta(self, nSource):
        source = [x for x in self.lista_kont if x.get_numer() == nSource][0]
        source.print_hist()

class KontoBankowe:
    def __init__(self , imie=None , nazwisko=None , numer_konta=None , saldo=0 ):
        self.imie = imie
        self.nazwisko = nazwisko
        self.__numer_konta = numer_konta # pole prywatne
        self.__saldo = saldo # pole prywatne
        self.__historia = [['Operacja','Kwota','Saldo']] # pole prywatne

    def wplata(self , kwota ):
        self.__saldo += kwota
        self.__historia.append(['+',kwota,self.__saldo])

    def wyplata(self , kwota ):
        if self.__saldo >= kwota:
            self.__saldo -= kwota
            self.__historia.append(['-',kwota,self.__saldo])
            return 1
        else:
            print("Nie masz wystarczajacych srodkow na koncie")
            return 0

    def get_saldo (self):
        return self.__saldo

    def get_numer (self):
        return self.__numer_konta

    def przelew(self,target,kwota):
        if self.wyplata(kwota):
            target.wplata(kwota)
        else:
            print("Przelew nieudany")

    def print_hist (self):
        print('Historia konta: ' + self.imie + ' ' + self.nazwisko + ' ' + str(self.__numer_konta))
        print(tab(self.__historia, headers='firstrow', tablefmt='grid'))


# B = Bank()
# B.utworzKonto('Jacek','Wodecki',1234)
# B.utworzKonto('Jan','Kowalski',2345)
# B.utworzKonto('aaa','sss',2346)
# B.wplata(1234,100)
# B.printKonta()
# B.przelew(1234,2345,20)
# B.wyplata(1234,30)
# B.printKonta()
# B.historia_konta(1234)


# K1= KontoBankowe('Jacek','Wodecki',1234)
# K2= KontoBankowe('Jan','Kowalski',2345)
# print(K1.get_saldo())
# K1.wplata(100)
# print(K1.get_saldo())
# K1.wyplata(50)
# print(K1.get_saldo())
#
# K1.przelew(K2,20)
# K1.print_hist()
# K2.print_hist()
# print(K1.__numer_konta)
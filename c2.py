from tabulate import tabulate as tab

class Bank:
    def __init__(self):
        self.lista_kont =
        self.tabelka = [['Imie','Naz<PERSON><PERSON>','Numer','<PERSON><PERSON>']]

    def utworzKonto(self,imie, nazwisko, numer_konta, saldo):
        asdasasd
        K = KontoBankowe(imie, nazwisko, numer_konta, saldo)
        self.tabelka.append([imie, nazwisko, numer_konta, saldo])

    def printKonta(self):
        asdd
        # for
        #     self.tabelka.append([self.lista_kont[i].imie, nazwisko, numer_konta, saldo])
        print(tab(self.tabelka))

    def przelew(self, nSource, nTarget, kwota):
        asdasd

    def wplata(self, nSource, kwota):
        source =
        source.wplata(kwota)

    def wyplata(self, nSource, kwota):
        asdasd

    def historia_konta(self, nSource):
        asdasd

class KontoBankowe:
    def __init__(self , imie=None , nazwisko=None , numer_konta=None , saldo=0 ):
        self.imie = imie
        self.nazwisko = nazwisko
        self.__numer_konta = numer_konta # pole prywatne
        self.__saldo = saldo # pole prywatne
        self.__historia = [['Operacja','Kwota','Saldo']] # pole prywatne

    def wplata(self , kwota ):
        self.__saldo += kwota
        self.__historia.append(['+',kwota,self.__saldo])

    def wyplata(self , kwota ):
        if self.__saldo >= kwota:
            self.__saldo -= kwota
            self.__historia.append(['-',kwota,self.__saldo])
            return 1
        else:
            print("Nie masz wystarczajacych srodkow na koncie")
            return 0

    def get_saldo (self):
        return self.__saldo

    def get_numer (self):
        return self.__numer_konta

    def przelew(self,target,kwota):
        if self.wyplata(kwota):
            target.wplata(kwota)
        else:
            print("Przelew nieudany")

    def print_hist (self):
        print('Historia konta: ' + self.imie + ' ' + self.nazwisko + ' ' + str(self.__numer_konta))
        print(tab(self.__historia, headers='firstrow', tablefmt='grid'))


B=Bank()
B.utworzKonto('Jacek','Wodecki',1234)
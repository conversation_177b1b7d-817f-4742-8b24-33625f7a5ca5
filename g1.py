from tabulate import tabulate as tab

class Bank:
    def __init__(self):
        self.lista_kont = []

    def utworzKonto(self, imie, nazwisko, numer_konta):
        # 1. sprawdz czy numer nie jest zajety
        blokada = len([x for x in self.lista_kont if x.get_numer() == numer_konta])
        # 2. je<PERSON><PERSON> to możliwe utwórz konto
        # 3. dodaj obiekt konta do listy kont

    def printKonta(self):
        # TABELKA: imie, nazwisko, numer, saldo

    def przelew(self):
        # znajdz konto zrodlowe i docelowe wg numeru
        source = [x for x in self.lista_kont if x.get_numer() == nSource][0]
        target = next(filter(lambda x: x.get_numer() == nTarget, self.lista_kont))
        # wykonaj przelew wykorzystujac metode przelew z klasy konto

    def wplata(self):

    def wyplata(self):

    def historia_konta(self):


class KontoBankowe:
    def __init__(self, imie, nazwisko, numer):
        self.__numer_konta = numer
        self.__saldo = 0
        self.__historia = [['Operacja','Kwota','Saldo']]
        self.imie = imie
        self.nazwisko = nazwisko

    def get_saldo(self):
        return self.__saldo

    def get_numer (self):
        return self.__numer_konta

    def wplata(self, kwota):
        self.__saldo += kwota
        self.__historia.append(['+', kwota, self.__saldo])

    def wyplata(self, kwota):
        if self.__saldo >= kwota:
            self.__saldo -= kwota
            self.__historia.append(['-', kwota, self.__saldo])
            return 1
        else:
            print("Nie masz wystarczajacych srodkow na koncie")
            return 0

    def przelew(self,target, kwota):
        if self.wyplata(kwota):
            target.wplata(kwota)
        else:
            print("Przelew nieudany")

    def print_hist(self):
        print('Historia konta: ' + self.imie + ' ' + self.nazwisko + ' ' + str(self.__numer_konta))
        print(tab(self.__historia, headers='firstrow', tablefmt='grid'))


B = Bank()
B.utworzKonto('Jacek','Wodecki',1234)
B.utworzKonto('Jan','Kowalski',2345)
B.utworzKonto('aaa','sss',2346)
B.wplata(1234,100)
B.printKonta()
B.przelew(1234,2345,20)
B.wyplata(1234,30)
B.printKonta()
B.historia_konta(1234)
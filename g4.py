from tabulate import tabulate as tab

class KontoBankowe:
    def __init__(self,imie,nazwisko,numer):
        self.imie = imie
        self.nazwisko = nazwisko
        self.__numer_konta = numer
        self.__saldo = 0
        self.__historia = [['Opera<PERSON>ja','Kwota','Saldo']]

    def wplata(self,kwota):
        self.__saldo += kwota
        self.__historia.append(['+',kwota,self.__saldo])

    def wyplata(self,kwota):
        if self.__saldo >= kwota:
            self.__saldo -= kwota
            self.__historia.append(['-',kwota,self.__saldo])
            return 1
        else:
            print("Nie masz wystarczajacych srodkow na koncie")
            return 0

    def get_saldo(self):
        return self.__saldo

    def get_historia(self):
        print(tab(self.__historia, headers='firstrow', tablefmt='grid'))

    def przelew(self,odbiorca,kwota):
        if self.wyplata(kwota) == True:
            odbiorca.wplata(kwota)
        else:
            print('Przelew niemożliwy')

K1 = KontoBankowe('Jacek','Wodecki',1234)
K2 = KontoBankowe('Jan','Kowalski',2345)
print(K1.get_saldo())
K1.wplata(100)
print(K1.get_saldo())
K1.wyplata(30)
print(K1.get_saldo())
K1.get_historia()
K1.przelew(K2,50)
# K1.get_historia()
# K2.get_historia()

from tabulate import tabulate as tab

class KontoBankowe:
    def __init__(self,imie,nazwisko,saldo,numer):
        self.imie = imie
        self.nazwisko = nazwisko
        self.__numer_konta = numer
        self.__saldo = saldo
        self.__historia = [['<PERSON><PERSON>ja','Kwota','Sal<PERSON>']]

    def wplata(self,kwota):
        self.__saldo += kwota
        self.__historia.append(['+',kwota,self.__saldo])

    def wyplata(self, kwota):
        if self.__saldo >= kwota:
            self.__saldo -= kwota
            self.__historia.append(['-', kwota, self.__saldo])
            return 1
        else:
            print("Nie masz wystarczajacych srodkow na koncie")
            return 0

    def get_historia(self):
        print(tab(self.__historia, headers='firstrow', tablefmt='fancy_grid'))

    def przelew(self,kwota,target):
        if self.wyplata(kwota):
            target.wplata(kwota)
        else:
            print("Prz<PERSON>w nieudany")


K1 = KontoBankowe('Jace<PERSON>','W<PERSON><PERSON>',0,1234)
K2 = KontoBankowe('<PERSON>','Kowalski',0,2345)

K1.wplata(100)
K1.wyplata(10)
K1.przelew(20,K2)
K1.get_historia()
K2.get_historia()
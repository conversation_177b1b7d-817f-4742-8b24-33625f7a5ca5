
class KontoBankowe:           #Definiujemy klasę konto bankowe, w której zawarte są funkcje umożliwiające wpłatę wypłatę i przelew, które zapisują każdą operację do historii danego konta.
    def __init__(self, imie, nazwisko, numer, saldo=0):
        self.imie = imie
        self.nazwisko = nazwisko
        self.numer = numer
        self.saldo = saldo
        self.historia_operacji = [] #historia ma 2 elementy: typ operacji(wplata/wyplata) oraz jej kwota

    def wplata(self, kwota):
        self.saldo += kwota
        self.historia_operacji.append({"typ": "wplata", "kwota": kwota})

    def wyplata(self, kwota):
        if kwota > self.saldo:  #zabezpieczenie przed przekroczeniem salda
            print("<PERSON>rak wystarczających środków.")
        else:
            self.saldo -= kwota
            self.historia_operacji.append({"typ": "wyplata", "kwota": kwota})

    def przelew(self, konto, kwota):
        if kwota > self.saldo:  #zabezpieczenie przed przekroczeniem salda
            print("Brak wystarczających środków.")
        else:
            self.saldo -= kwota
            self.historia_operacji.append({"typ": "przelew_wychodzacy", "kwota": kwota})
            konto.wplata(kwota)
            konto.historia_operacji.append({"typ": "przelew_przychodzacy", "kwota": kwota})


class Bank: #Definiujemy klasę bank, która ma tylko jeden element czyli listę "konta"
    def __init__(self):
        self.konta = {}

    def utworz_konto(self, imie, nazwisko, numer):      #funkcja tworzy nowe konto z podanych danych
        if numer in self.konta:      #Zabezpieczenie przed powtórzeniem się numeru konta
            print("Numer konta jest już w użyciu.")
        else:
            konto = KontoBankowe(imie, nazwisko, numer)
            self.konta[numer] = konto
            print(f"Utworzono konto o numerze {numer}.")

    def get_konto(self, numer): # funkcja zwraca obiekt klasy KontoBankowe o podanym numerze, używamy jej by wykonać jakąś operację na koncie
        return self.konta.get(numer)

    def wyswietl_liste_kont(self): #Wyswietla listę wszystkich kont, zawiera imie, nazwisko i kwotę
        print("Lista kont:")
        for numer, konto in self.konta.items():
            print(f"Numer konta: {numer}, Imię i nazwisko: {konto.imie} {konto.nazwisko}, Saldo: {konto.saldo}")

    def historia_konta(self, numer):  #wyswietla historię transakcji danego konta, wyświetlając jej typ i kwotę.
        konto = self.get_konto(numer) # przykład wykorzystania funkcji get_konto
        if konto:
            print(f"Historia operacji konta nr {numer}:")
            for operacja in konto.historia_operacji:
                print(f"- typ: {operacja['typ']}, kwota: {operacja['kwota']}")
        else:
            print(f"Nie znaleziono konta o numerze {numer}.")  #zabezpieczenie przed wpisaniem nieistniejącego numeru konta

    def wplata(self, numer, kwota):
        konto = self.get_konto(numer)
        if konto:
            konto.wplata(kwota)
            print(f"Wpłacono {kwota} na konto nr {numer}.")
        else:
            print("Nie znaleziono konta o podanym numerze.")  #zabezpieczenie przed wpisaniem nieistniejącego numeru konta

    def wyplata(self, numer, kwota):
        konto = self.get_konto(numer)
        if konto:
            konto.wyplata(kwota)
            print(f"Wypłacono {kwota} z konta nr {numer}.")
        else:
            print("Nie znaleziono konta o podanym numerze")  #zabezpieczenie przed wpisaniem nieistniejącego numeru konta
    def przelew(self, numer_nadawcy, numer_odbiorcy, kwota):
        konto_nadawcy = self.get_konto(numer_nadawcy)
        konto_odbiorcy = self.get_konto(numer_odbiorcy)
        if not konto_nadawcy:
            print("Nie znaleziono konta nadawcy.")  #zabezpieczenie przed wpisaniem nieistniejącego numeru konta
        elif not konto_odbiorcy:
            print("Nie znaleziono konta odbiorcy.")  #zabezpieczenie przed wpisaniem nieistniejącego numeru konta
        elif kwota > konto_nadawcy.saldo:
            print("Brak wystarczających środków na koncie nadawcy.") #zabezpieczenie przed zrobieniem przelewu o większej kwocie niż saldo
        else:
            konto_nadawcy.wyplata(kwota)
            konto_odbiorcy.wplata(kwota)
            print(f"Zrealizowano przelew z konta nr {numer_nadawcy} na konto nr {numer_odbiorcy} w kwocie {kwota}.")

B=Bank() #Poniżej kilka przykładowych operacji na kontach.
B.utworz_konto('Tomasz','Janeczek',1)
B.utworz_konto('Konrad','Langer',2)
B.utworz_konto('Radosław','Kiełek',3)
B.wplata(1, 100)
B.przelew(1, 2, 50)
B.wyplata(2,45)
B.wyswietl_liste_kont()
B.historia_konta(1)
B.historia_konta(2)

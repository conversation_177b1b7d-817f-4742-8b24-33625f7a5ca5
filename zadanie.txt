Zadanie: System bankowy
Napisz program, który symuluje działanie systemu bankowego. Program powinien
spełniać następujące wymagania:

1. Program powinien umożliwiać tworzenie nowych kont bankowych z unikalnym numerem i saldem początkowym.
2. Program powinien umożliwiać wpłaty oraz wypłaty środków z konta.
3. Program powinien umożliwiać wyświetlanie stanu konta oraz historii transakcji.
4. Program powinien umożliwiać przeprowadzanie transakcji między kontami bankowymi.
5. Program powinien zabezpieczać transakcje przed przekroczeniem salda na koncie.

1. Klasa banku posiada metodę obsługującą zakładanie nowych kont. Metoda musi
sprawdzić czy można założyć konto o danym numerze (czy ten numer nie jest już <PERSON>aj<PERSON>).
2. Klasa banku musi przechowywać listę kont w jakiejś formie.
3. Klasa banku musi obsługiwać wpłaty, wypłaty i przelewy, a argumentami są numery
kont. Wykorzystujemy istniejące metody klasy konto.
4. Klasa banku posiada metodę wyświetlającą listę kont z istotnymi informacjami.
5. Klasa banku posiada metodę wyświetlającą historię konta, a argumentem jest numer konta.
6. Klasa konta musi posiadać metody obsługujące elementarne operacje (z tych metod będzie
korzystać klasa banku), czyli wpłata, wypłata, przelew, wyświetlanie historii konta

from tabulate import tabulate as tab
self.historia = [['Operacja','Kwota','Saldo']]
print(tab(historia, headers='firstrow', tablefmt='grid')

pip install tabulate

self.__saldo = saldo
def get_saldo(self):
    return self.__saldo
def __init__(self , imie=None , nazwisko=None , numer_konta=None , saldo=0 ):

B = Bank()
B.utworzKonto('Jacek','Wodecki',1234)
B.utworzKonto('Jan','Kowalski',2345)
B.utworzKonto('aaa','sss',2346)
B.wplata(1234,100)
B.printKonta()
B.przelew(1234,2345,20)
B.wyplata(1234,30)
B.printKonta()
B.historia_konta(1234)